import amqp, { Channel, Connection } from "amqplib";
import config from "../config/config";

// Notification types for the enhanced notification system
export type NotificationTargetType =
  | "single" // Send to specific user ID
  | "all_clients" // Send to all clients
  | "all_vendors" // Send to all vendors
  | "group"; // Send to current group

export interface NotificationPayload {
  targetType: NotificationTargetType;
  userId?: string | string[]; // Required for "single" type, optional for others
  title: string;
  message: string;
  data?: Record<string, unknown>;
}
class RabbitMQService {
  private correlationMap = new Map<
    string,
    ({
      count,
      connected,
      latestMessageAt,
    }: {
      count: number;
      connected: boolean;
      latestMessageAt: string | null;
    }) => void
  >();
  private connection!: Connection;
  private channel!: Channel;

  constructor() {
    this.init();
  }

  async init() {
    try {
      this.connection = await amqp.connect(config.msgBrokerURL!);
      this.channel = await this.connection.createChannel();
      await this.channel.assertQueue(config.queue.notifications);
      await this.channel.assertQueue(config.queue.emailQueue);
    } catch (error) {
      console.error("Failed to initialize RabbitMQ connection:", error);
    }
  }

  async sendEmailNotification(to: string, subject: string, body: string) {
    const message = { to, subject, body };
    this.channel.sendToQueue(
      config.queue.emailQueue,
      Buffer.from(JSON.stringify(message))
    );
  }

  async notifyReceiver(
    receiverId: string,
    title: string,
    message: string,
    data?: object
  ) {
    console.log("Sending notification for receiver:", receiverId);
    const notificationPayload = {
      userId: receiverId,
      message,
      title,
      data: data || {},
    };

    try {
      await this.channel.assertQueue(config.queue.notifications);
      console.log("Sending notification payload:", notificationPayload);
      this.channel.sendToQueue(
        config.queue.notifications,
        Buffer.from(JSON.stringify(notificationPayload))
      );
      console.log("Notification sent to queue successfully");
    } catch (error) {
      console.error("Failed to send notification:", error);
      throw error;
    }
  }

  /**
   * Send notification to a specific user using new format
   */
  async notifyUser(
    userId: string,
    title: string,
    message: string,
    data?: Record<string, unknown>
  ) {
    const payload: NotificationPayload = {
      targetType: "single",
      userId,
      title,
      message,
      data,
    };
    await this.sendNotification(payload);
  }

  /**
   * Send notification to multiple users
   */
  async notifyUsers(
    userIds: string[],
    title: string,
    message: string,
    data?: Record<string, unknown>
  ) {
    const payload: NotificationPayload = {
      targetType: "single",
      userId: userIds,
      title,
      message,
      data,
    };
    await this.sendNotification(payload);
  }

  /**
   * Send notification to all clients
   */
  async notifyAllClients(
    title: string,
    message: string,
    data?: Record<string, unknown>
  ) {
    const payload: NotificationPayload = {
      targetType: "all_clients",
      title,
      message,
      data,
    };
    await this.sendNotification(payload);
  }

  /**
   * Send notification to all vendors
   */
  async notifyAllVendors(
    title: string,
    message: string,
    data?: Record<string, unknown>
  ) {
    const payload: NotificationPayload = {
      targetType: "all_vendors",
      title,
      message,
      data,
    };
    await this.sendNotification(payload);
  }

  /**
   * Send notification to a group of users
   */
  async notifyGroup(
    userIds: string[],
    title: string,
    message: string,
    data?: Record<string, unknown>
  ) {
    const payload: NotificationPayload = {
      targetType: "group",
      userId: userIds,
      title,
      message,
      data,
    };
    await this.sendNotification(payload);
  }

  /**
   * Generic method to send notification with target type
   */
  async sendNotificationByType(
    targetType: NotificationTargetType,
    title: string,
    message: string,
    data?: Record<string, unknown>,
    userId?: string | string[]
  ) {
    const payload: NotificationPayload = {
      targetType,
      title,
      message,
      data,
      userId,
    };
    await this.sendNotification(payload);
  }

  /**
   * Send message to notification service for new product in favorite brand
   */
  async notifyNewProductInFavoriteBrand(
    productId: string,
    brandId: string,
    productName: string,
    brandName: string
  ) {
    const payload = {
      type: "new_product_in_favorite_brand",
      productId,
      brandId,
      productName,
      brandName,
    };

    try {
      await this.channel.assertQueue("FAVORITES_NOTIFICATIONS");
      console.log(
        "🎯 Sending FAVORITES notification to FAVORITES_NOTIFICATIONS queue:",
        payload
      );
      this.channel.sendToQueue(
        "FAVORITES_NOTIFICATIONS",
        Buffer.from(JSON.stringify(payload))
      );
      console.log(
        "✅ Sent new product in favorite brand notification to FAVORITES_NOTIFICATIONS queue"
      );
    } catch (error) {
      console.error("❌ Failed to send new product notification:", error);
      throw error;
    }
  }

  /**
   * Send message to notification service for product update
   */
  async notifyProductUpdate(
    productId: string,
    productName: string,
    brandName: string,
    updateType: "price" | "details" | "general",
    oldPrice?: number,
    newPrice?: number
  ) {
    const payload = {
      type: "product_update",
      productId,
      productName,
      brandName,
      updateType,
      oldPrice,
      newPrice,
    };

    try {
      await this.channel.assertQueue("FAVORITES_NOTIFICATIONS");
      this.channel.sendToQueue(
        "FAVORITES_NOTIFICATIONS",
        Buffer.from(JSON.stringify(payload))
      );
      console.log("Sent product update notification to queue");
    } catch (error) {
      console.error("Failed to send product update notification:", error);
      throw error;
    }
  }

  /**
   * Private method to send notification to queue
   */
  private async sendNotification(payload: NotificationPayload) {
    try {
      await this.channel.assertQueue(config.queue.notifications);
      console.log("Sending enhanced notification payload:", payload);
      this.channel.sendToQueue(
        config.queue.notifications,
        Buffer.from(JSON.stringify(payload))
      );
      console.log("Enhanced notification sent to queue successfully");
    } catch (error) {
      console.error("Failed to send enhanced notification:", error);
      throw error;
    }
  }
}
export const rabbitMQService = new RabbitMQService();
