import mongoose, { Schema, Document } from "mongoose";

// Interface for TypeScript typing
export interface IClient extends Document {
  profile: mongoose.Types.ObjectId;
  name: string;
  createdAt: Date;
  activities: any[]; // Activities can hold any type of data
  email: string;
  points: number;
  favorite_brands: mongoose.Types.ObjectId[];
  favorite_products: mongoose.Types.ObjectId[];
  phone_number: string;
  location: mongoose.Types.ObjectId[];
  phone_number_verified: boolean;
  notifications: mongoose.Types.ObjectId[];
}

// Schema Definition
const clientSchema = new Schema<IClient>(
  {
    profile: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Profile",
    },
    name: { type: String },
    email: {
      type: String,
      unique: true,
      lowercase: true, // Converts to lowercase
      trim: true,
    },
    points: { type: Number, default: 0 },
    createdAt: { type: Date, default: Date.now },
    activities: [{ type: Schema.Types.Mixed }], // Accepts any type of data
    favorite_brands: [{ type: mongoose.Schema.Types.ObjectId, ref: "Brand" }],
    favorite_products: [
      { type: mongoose.Schema.Types.ObjectId, ref: "Product" },
    ],
    phone_number: { type: String },
    location: [{ type: mongoose.Schema.Types.ObjectId, ref: "Location" }],
    phone_number_verified: { type: Boolean, default: false },
    notifications: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Notification",
      },
    ],
  },
  { timestamps: true }
);

// Exporting the Model
const Client = mongoose.model<IClient>("Client", clientSchema);
export default Client;
