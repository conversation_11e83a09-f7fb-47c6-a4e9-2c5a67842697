import express from "express";
import { notificationService, FavoritesNotificationService } from "../services";
import { NotificationTargetType } from "../types/notification";

const router = express.Router();
const favoritesNotificationService = new FavoritesNotificationService();

/**
 * Send notification to a specific user
 * POST /api/notifications/send-to-user
 * Body: { userId: string, title: string, message: string, data?: object }
 */
router.post("/send-to-user", async (req: any, res: any) => {
  try {
    const { userId, title, message, data } = req.body;

    if (!userId || !title || !message) {
      return res.status(400).json({
        error: "userId, title, and message are required",
      });
    }

    await notificationService.sendToUser(userId, title, message, data);

    res.status(200).json({
      success: true,
      message: "Notification sent to user successfully",
    });
  } catch (error) {
    console.error("Error sending notification to user:", error);
    res.status(500).json({
      error: "Failed to send notification",
    });
  }
});

/**
 * Send notification to multiple users
 * POST /api/notifications/send-to-users
 * Body: { userIds: string[], title: string, message: string, data?: object }
 */
router.post("/send-to-users", async (req: any, res: any) => {
  try {
    const { userIds, title, message, data } = req.body;

    if (!userIds || !Array.isArray(userIds) || !title || !message) {
      return res.status(400).json({
        error: "userIds (array), title, and message are required",
      });
    }

    await notificationService.sendToUsers(userIds, title, message, data);

    res.status(200).json({
      success: true,
      message: "Notification sent to users successfully",
    });
  } catch (error) {
    console.error("Error sending notification to users:", error);
    res.status(500).json({
      error: "Failed to send notification",
    });
  }
});

/**
 * Send notification to all clients
 * POST /api/notifications/send-to-all-clients
 * Body: { title: string, message: string, data?: object }
 */
router.post("/send-to-all-clients", async (req: any, res: any) => {
  try {
    const { title, message, data } = req.body;

    if (!title || !message) {
      return res.status(400).json({
        error: "title and message are required",
      });
    }

    await notificationService.sendToAllClients(title, message, data);

    res.status(200).json({
      success: true,
      message: "Notification sent to all clients successfully",
    });
  } catch (error) {
    console.error("Error sending notification to all clients:", error);
    res.status(500).json({
      error: "Failed to send notification",
    });
  }
});

/**
 * Send notification to all vendors
 * POST /api/notifications/send-to-all-vendors
 * Body: { title: string, message: string, data?: object }
 */
router.post("/send-to-all-vendors", async (req: any, res: any) => {
  try {
    const { title, message, data } = req.body;

    if (!title || !message) {
      return res.status(400).json({
        error: "title and message are required",
      });
    }

    await notificationService.sendToAllVendors(title, message, data);

    res.status(200).json({
      success: true,
      message: "Notification sent to all vendors successfully",
    });
  } catch (error) {
    console.error("Error sending notification to all vendors:", error);
    res.status(500).json({
      error: "Failed to send notification",
    });
  }
});

/**
 * Send notification to a group
 * POST /api/notifications/send-to-group
 * Body: { userIds: string[], title: string, message: string, data?: object }
 */
router.post("/send-to-group", async (req: any, res: any) => {
  try {
    const { userIds, title, message, data } = req.body;

    if (!userIds || !Array.isArray(userIds) || !title || !message) {
      return res.status(400).json({
        error: "userIds (array), title, and message are required",
      });
    }

    await notificationService.sendToGroup(userIds, title, message, data);

    res.status(200).json({
      success: true,
      message: "Notification sent to group successfully",
    });
  } catch (error) {
    console.error("Error sending notification to group:", error);
    res.status(500).json({
      error: "Failed to send notification",
    });
  }
});

/**
 * Generic notification endpoint with target type
 * POST /api/notifications/send
 * Body: { targetType: string, title: string, message: string, data?: object, userId?: string | string[] }
 */
router.post("/send", async (req: any, res: any) => {
  try {
    const { targetType, title, message, data, userId } = req.body;

    if (!targetType || !title || !message) {
      return res.status(400).json({
        error: "targetType, title, and message are required",
      });
    }

    // Validate targetType
    const validTargetTypes: NotificationTargetType[] = [
      "single",
      "all_clients",
      "all_vendors",
      "group",
    ];
    if (!validTargetTypes.includes(targetType)) {
      return res.status(400).json({
        error: `Invalid targetType. Must be one of: ${validTargetTypes.join(
          ", "
        )}`,
      });
    }

    // Validate userId for types that require it
    if ((targetType === "single" || targetType === "group") && !userId) {
      return res.status(400).json({
        error: `userId is required for targetType: ${targetType}`,
      });
    }

    await notificationService.sendNotificationByType(
      targetType,
      title,
      message,
      data,
      userId
    );

    res.status(200).json({
      success: true,
      message: `Notification sent with targetType: ${targetType}`,
    });
  } catch (error) {
    console.error("Error sending notification:", error);
    res.status(500).json({
      error: "Failed to send notification",
    });
  }
});

/**
 * Notify clients when a new product is added to a brand they have favorited
 * POST /api/notifications/new-product-in-favorite-brand
 * Body: { productId: string, brandId: string, productName: string, brandName: string }
 */
router.post("/new-product-in-favorite-brand", async (req: any, res: any) => {
  try {
    const { productId, brandId, productName, brandName } = req.body;

    if (!productId || !brandId || !productName || !brandName) {
      return res.status(400).json({
        error: "productId, brandId, productName, and brandName are required",
      });
    }

    const notificationIds =
      await favoritesNotificationService.notifyNewProductInFavoriteBrand(
        productId,
        brandId,
        productName,
        brandName
      );

    res.status(200).json({
      success: true,
      message: "Notification sent to clients with favorite brand",
      notificationIds,
      clientsNotified: notificationIds.length,
    });
  } catch (error) {
    console.error("Error sending new product notification:", error);
    res.status(500).json({
      error: "Failed to send notification",
    });
  }
});

/**
 * Notify clients when a product they have favorited is updated
 * POST /api/notifications/product-update
 * Body: { productId: string, productName: string, brandName: string, updateType: string, oldPrice?: number, newPrice?: number }
 */
router.post("/product-update", async (req: any, res: any) => {
  try {
    const {
      productId,
      productName,
      brandName,
      updateType,
      oldPrice,
      newPrice,
    } = req.body;

    if (!productId || !productName || !brandName || !updateType) {
      return res.status(400).json({
        error: "productId, productName, brandName, and updateType are required",
      });
    }

    const validUpdateTypes = ["price", "details", "general"];
    if (!validUpdateTypes.includes(updateType)) {
      return res.status(400).json({
        error: `Invalid updateType. Must be one of: ${validUpdateTypes.join(
          ", "
        )}`,
      });
    }

    const notificationIds =
      await favoritesNotificationService.notifyProductUpdate(
        productId,
        productName,
        brandName,
        updateType,
        oldPrice,
        newPrice
      );

    res.status(200).json({
      success: true,
      message: "Notification sent to clients with favorite product",
      notificationIds,
      clientsNotified: notificationIds.length,
    });
  } catch (error) {
    console.error("Error sending product update notification:", error);
    res.status(500).json({
      error: "Failed to send notification",
    });
  }
});

/**
 * Get count of clients with specific brand in favorites
 * GET /api/notifications/favorite-brand-count/:brandId
 */
router.get("/favorite-brand-count/:brandId", async (req: any, res: any) => {
  try {
    const { brandId } = req.params;

    if (!brandId) {
      return res.status(400).json({
        error: "brandId is required",
      });
    }

    const count =
      await favoritesNotificationService.getClientsCountWithFavoriteBrand(
        brandId
      );

    res.status(200).json({
      success: true,
      brandId,
      clientsCount: count,
    });
  } catch (error) {
    console.error("Error getting favorite brand count:", error);
    res.status(500).json({
      error: "Failed to get count",
    });
  }
});

/**
 * Get count of clients with specific product in favorites
 * GET /api/notifications/favorite-product-count/:productId
 */
router.get("/favorite-product-count/:productId", async (req: any, res: any) => {
  try {
    const { productId } = req.params;

    if (!productId) {
      return res.status(400).json({
        error: "productId is required",
      });
    }

    const count =
      await favoritesNotificationService.getClientsCountWithFavoriteProduct(
        productId
      );

    res.status(200).json({
      success: true,
      productId,
      clientsCount: count,
    });
  } catch (error) {
    console.error("Error getting favorite product count:", error);
    res.status(500).json({
      error: "Failed to get count",
    });
  }
});

export default router;
