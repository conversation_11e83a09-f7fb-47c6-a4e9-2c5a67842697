import { Client, Product, Brand } from "../database";
import { ExpoPushService } from "./ExpoPushService";

export class FavoritesNotificationService {
  private expoPushService: ExpoPushService;

  constructor() {
    this.expoPushService = new ExpoPushService();
  }

  /**
   * Notify clients who have a specific brand in their favorites
   */
  async notifyClientsWithFavoriteBrand(
    brandId: string,
    title: string,
    message: string,
    data?: Record<string, unknown>
  ): Promise<string[]> {
    try {
      // Find all clients who have this brand in their favorites
      const clientsWithFavoriteBrand = await Client.find({
        favorite_brands: brandId,
      }).select("profile");

      if (clientsWithFavoriteBrand.length === 0) {
        console.log(`No clients found with brand ${brandId} in favorites`);
        return [];
      }

      // Extract profile IDs
      const profileIds = clientsWithFavoriteBrand.map((client) =>
        client.profile.toString()
      );

      console.log(
        `Found ${profileIds.length} clients with brand ${brandId} in favorites`
      );

      // Send notifications to these clients
      const notificationIds = await this.expoPushService.sendPushNotification(
        profileIds,
        title,
        message,
        data
      );

      return notificationIds;
    } catch (error) {
      console.error("Error notifying clients with favorite brand:", error);
      throw error;
    }
  }

  /**
   * Notify clients who have a specific product in their favorites
   */
  async notifyClientsWithFavoriteProduct(
    productId: string,
    title: string,
    message: string,
    data?: Record<string, unknown>
  ): Promise<string[]> {
    try {
      // Find all clients who have this product in their favorites
      const clientsWithFavoriteProduct = await Client.find({
        favorite_products: productId,
      }).select("profile");

      if (clientsWithFavoriteProduct.length === 0) {
        console.log(`No clients found with product ${productId} in favorites`);
        return [];
      }

      // Extract profile IDs
      const profileIds = clientsWithFavoriteProduct.map((client) =>
        client.profile.toString()
      );

      console.log(
        `Found ${profileIds.length} clients with product ${productId} in favorites`
      );

      // Send notifications to these clients
      const notificationIds = await this.expoPushService.sendPushNotification(
        profileIds,
        title,
        message,
        data
      );

      return notificationIds;
    } catch (error) {
      console.error("Error notifying clients with favorite product:", error);
      throw error;
    }
  }

  /**
   * Notify clients when a new product is added to a brand they have favorited
   */
  async notifyNewProductInFavoriteBrand(
    productId: string,
    brandId: string,
    productName: string,
    brandName: string
  ): Promise<string[]> {
    const title = "New Product Available! 🛍️";
    const message = `${brandName} just added "${productName}" to their collection. Check it out!`;
    const data = {
      type: "new_product",
      productId: productId,
      productName: productName,
      brandId: brandId,
      brandName: brandName,
      action: "view_product",
      url: {
        pathname: `/(client)/(screens)/products/${productId}/product`,
      },
    };

    return await this.notifyClientsWithFavoriteBrand(
      brandId,
      title,
      message,
      data
    );
  }

  /**
   * Notify clients when a product they have favorited is updated (price change, etc.)
   */
  async notifyProductUpdate(
    productId: string,
    productName: string,
    brandName: string,
    updateType: "price" | "details" | "general",
    oldPrice?: number,
    newPrice?: number
  ): Promise<string[]> {
    let title: string;
    let message: string;

    switch (updateType) {
      case "price":
        title = "Price Update! 💰";
        if (oldPrice && newPrice) {
          const priceChange = newPrice < oldPrice ? "reduced" : "updated";
          message = `Great news! The price for "${productName}" by ${brandName} has been ${priceChange}. Check it out now!`;
        } else {
          message = `The price for "${productName}" by ${brandName} has been updated. Check it out now!`;
        }
        break;
      case "details":
        title = "Product Updated! ✨";
        message = `"${productName}" by ${brandName} has been updated with new details. Take a look!`;
        break;
      default:
        title = "Product Updated! ✨";
        message = `"${productName}" by ${brandName} has been updated. Check out what's new!`;
    }

    const data = {
      type: "product_update",
      productId: productId,
      productName: productName,
      brandName: brandName,
      updateType: updateType,
      oldPrice: oldPrice,
      newPrice: newPrice,
      action: "view_product",
      url: {
        pathname: `/(client)/(screens)/products/${productId}/product`,
      },
    };

    return await this.notifyClientsWithFavoriteProduct(
      productId,
      title,
      message,
      data
    );
  }

  /**
   * Get clients count with specific brand in favorites (for analytics)
   */
  async getClientsCountWithFavoriteBrand(brandId: string): Promise<number> {
    try {
      const count = await Client.countDocuments({
        favorite_brands: brandId,
      });
      return count;
    } catch (error) {
      console.error("Error getting clients count with favorite brand:", error);
      return 0;
    }
  }

  /**
   * Get clients count with specific product in favorites (for analytics)
   */
  async getClientsCountWithFavoriteProduct(productId: string): Promise<number> {
    try {
      const count = await Client.countDocuments({
        favorite_products: productId,
      });
      return count;
    } catch (error) {
      console.error("Error getting clients count with favorite product:", error);
      return 0;
    }
  }
}
