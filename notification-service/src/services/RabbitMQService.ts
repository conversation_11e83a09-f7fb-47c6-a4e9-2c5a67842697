import amqp, { Channel } from "amqplib";
import config from "../config/config";
import { ExpoPushService } from "./ExpoPushService";
import { EmailService } from "./EmailService";
import { FavoritesNotificationService } from "./FavoritesNotificationService";
import {
  NotificationPayload,
  LegacyNotificationPayload,
} from "../types/notification";

class RabbitMQService {
  private channel!: Channel;
  private expoPushService = new ExpoPushService();
  private emailService = new EmailService();
  private favoritesNotificationService = new FavoritesNotificationService();

  constructor() {
    this.init();
  }

  async init() {
    const connection = await amqp.connect(config.msgBrokerURL!);
    this.channel = await connection.createChannel();
    await this.channel.assertQueue(config.queue.notifications);
    await this.channel.assertQueue(config.queue.emailQueue);
    await this.channel.assertQueue("FAVORITES_NOTIFICATIONS");
    await this.consumeNotification();
    await this.consumeEmailNotifications();
    await this.consumeFavoritesNotifications();
  }
  async consumeNotification() {
    console.log("Setting up consumer for notifications queue");
    this.channel.consume(config.queue.notifications, async (msg) => {
      if (!msg) {
        console.log("Received null message");
        return;
      }

      try {
        console.log("Received message:", msg.content.toString());
        const messageData = JSON.parse(msg.content.toString());

        // Check if it's the new notification format with targetType
        if (this.isNewNotificationFormat(messageData)) {
          const { targetType, userId, title, message, data } =
            messageData as NotificationPayload;

          console.log("Processing new format notification:", {
            targetType,
            userId,
            title,
            message,
            data,
          });

          await this.expoPushService.sendNotificationByType(
            targetType,
            title,
            message,
            data,
            userId
          );
        } else {
          // Handle legacy format for backward compatibility
          const { userId, message, title, data } =
            messageData as LegacyNotificationPayload;

          console.log("Processing legacy format notification:", {
            userId,
            title,
            message,
            data,
          });

          await this.expoPushService.sendPushNotification(
            userId,
            title,
            message,
            data
          );
        }

        console.log("Successfully sent push notification");
        this.channel.ack(msg);
      } catch (error) {
        console.error("Error processing notification:", error);
        // Don't requeue the message if it's malformed
        this.channel.nack(msg, false, false);
      }
    });
    console.log("Notification consumer setup complete");
  }

  private isNewNotificationFormat(
    messageData: any
  ): messageData is NotificationPayload {
    return messageData && typeof messageData.targetType === "string";
  }
  async consumeEmailNotifications() {
    this.channel.consume(config.queue.emailQueue, async (msg) => {
      if (msg) {
        const { to, subject, body } = JSON.parse(msg.content.toString());
        await this.emailService.sendEmail(to, subject, body);
        console.log(`Email sent to ${to}`);
        this.channel.ack(msg);
      }
    });
  }

  async consumeFavoritesNotifications() {
    this.channel.consume("FAVORITES_NOTIFICATIONS", async (msg) => {
      if (!msg) return;

      try {
        const messageData = JSON.parse(msg.content.toString());
        console.log(
          "🎯 Processing FAVORITES notification from FAVORITES_NOTIFICATIONS queue:",
          messageData
        );

        switch (messageData.type) {
          case "new_product_in_favorite_brand":
            await this.favoritesNotificationService.notifyNewProductInFavoriteBrand(
              messageData.productId,
              messageData.brandId,
              messageData.productName,
              messageData.brandName
            );
            break;

          case "product_update":
            await this.favoritesNotificationService.notifyProductUpdate(
              messageData.productId,
              messageData.productName,
              messageData.brandName,
              messageData.updateType,
              messageData.oldPrice,
              messageData.newPrice
            );
            break;

          default:
            console.warn(
              "Unknown favorites notification type:",
              messageData.type
            );
        }

        console.log("Successfully processed favorites notification");
        this.channel.ack(msg);
      } catch (error) {
        console.error("Error processing favorites notification:", error);
        // Don't requeue the message if it's malformed
        this.channel.nack(msg, false, false);
      }
    });
    console.log("Favorites notification consumer setup complete");
  }
}

export const rabbitMQService = new RabbitMQService();
